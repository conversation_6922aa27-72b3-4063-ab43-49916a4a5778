import { ioSendData } from '../app'
import { DATA_ORIGINS, SHOTX_COLLECTION_NAME } from '../constants/collections'
import { REPLIED_TRIGGER, TRIGGERS } from '../constants/triggers'
import { CodeChatInstanceStatusEnum } from '../enums'
import { EvolutionEvent } from '../enums/evolution/Event'
import { ContextModel } from '../libs/model/context'
import { LeadModel } from '../libs/model/lead'
import { socketResponseModel } from '../libs/model/socketResponse'
import { InstancesRepository } from '../libs/repositories/firestore/instance'
import { ShotXCronRepository } from '../libs/repositories/firestore/shotXCron'
import { WhatsappRepository } from '../libs/repositories/firestore/whatsapp'
import { MinioRepository } from '../libs/repositories/minio/api'
import { EvolutionService } from '../libs/services/axios/evolution'
import { saveShotxCronLogs } from '../libs/services/firestore/logs'
import { instanceStatusDto } from '../libs/types/codechat/instanceStatus'
import { MessageUpSert } from '../libs/types/evolution'
import { InstanceType } from '../libs/types/instance/instance'
import { MinioFile } from '../libs/types/minio/file'
import LeadType from '../libs/types/qiplus/lead'
import { SniperController } from '../platforms/sniper/controllers/sniper.controller'
import { SniperResponse } from '../platforms/sniper/types/sniper.response'
import { SessionRef } from '../platforms/sniper/types/sniper.session'
import { getExtensionFromMimeType } from '../utils/audio'
import { dateToString } from '../utils/dateUtils'
import {
  getPhoneFromContactId,
  sanitizeUpSertEventData,
} from '../utils/evolution'
import { mergeObject } from '../utils/objectUtils'
import LeadController from './lead'
import LogsController from './logs'
class WhatsappController {
  private userId: string
  private accountId: string
  private logs: any

  constructor(
    private readonly service: EvolutionService,
    private readonly repository: WhatsappRepository,
    private readonly minio: MinioRepository,
    private readonly sniper: SniperController,
    private readonly instances: InstancesRepository
  ) {
    this.userId = ''
    this.accountId = ''
    this.logs = LogsController
  }

  public check(): boolean {
    if (!this.accountId || !this.userId) {
      return false
    }
    return true
  }

  async status(instanceId: string, accountId: string) {
    return await this.service.status(instanceId, accountId, null)
  }

  async connect(instanceId: string, accountId: string) {
    const response = await this.service.connect(instanceId, accountId, null)
    await this.service.webhook(instanceId, accountId)
    return response
  }

  async disconnect(instanceId: string, accountId: string) {
    return await this.service.disconnect(instanceId, accountId, null)
  }

  async instanceDelete(instanceId: string, accountId: string) {
    const response = await this.service.delete(instanceId, accountId, null)
    ioSendData(instanceId, { state: 'deleted' })
    return response
  }

  async sendBroadcast(shotXCron: any) {
    console.log('SEND BROADCAST', shotXCron)
    const shotXCronRepository = new ShotXCronRepository()
    const { message, instance, id, contacts } = shotXCron
    shotXCronRepository.update(id, {
      executing: true,
    })

    //Todo criar uma funcao para validar cada item

    for (const contact of contacts) {
      if (!instance || !contact.contactRemoteId || !message) {
        saveShotxCronLogs(
          id,
          instance.accountId,
          instance.id,
          contact.id,
          TRIGGERS.ERROR,
          '',
          {
            error: true,
            data: 'instanceId, contact and message are required',
          }
        )
      } else {
        const result = await this.sendText(
          instance.ID,
          contact.contactRemoteId,
          message,
          instance.userId,
          instance.accountId,
          true,
          {
            id: id,
            contactId: contact.id,
            replied: false,
          }
        )
        if (result.error) {
          saveShotxCronLogs(
            id,
            instance.accountId,
            instance.id,
            contact.id,
            TRIGGERS.ERROR,
            '',
            result
          )
        } else {
          const message = result?.data.key?.id || null
          saveShotxCronLogs(
            id,
            instance.accountId,
            instance.id,
            contact.id,
            TRIGGERS.SEND_SUCCESS,
            message,
            result
          )
        }
      }
    }

    shotXCronRepository.update(id, {
      executing: false,
      executed: true,
    })
  }

  //TODO SendText
  async sendText(
    instanceId: string,
    phone: string,
    message: string,
    userId: string,
    accountId: string,
    saveIfFail = true,
    shotxCron?: any,
    contactId?: string
  ) {
    const resultSendText: any = await this.service.sendText(
      instanceId,
      accountId,
      phone,
      message
    )

    console.log('WHATSERROR', resultSendText)
    if (resultSendText.error) {
      return {
        error: true,
        data: resultSendText.data,
        message: resultSendText.message,
        status: resultSendText.status,
      }
    }

    let result = { error: false, data: {} }
    const messageResponse = resultSendText.data
    if (!saveIfFail && messageResponse.error) {
      return {
        error: true,
        data: messageResponse.data,
        message: messageResponse.message,
      }
    }

    // console.log('MESSAGE RESPONSE', messageResponse)
    if (shotxCron) {
      if (contactId) {
        messageResponse.contact_id = contactId
      }
      await this.repository.saveMessageSent(
        instanceId,
        accountId,
        userId,
        phone,
        resultSendText.error === true,
        messageResponse,
        shotxCron
      )
    } else {
      await this.repository.saveMessageSent(
        instanceId,
        accountId,
        userId,
        phone,
        resultSendText.error === true,
        messageResponse
      )
    }

    result = {
      error: resultSendText.error,
      data: resultSendText.data,
    }
    return result
  }

  async sendAudio(
    instanceId: string,
    accountId: string,
    phone: string,
    audio: string
  ) {
    const result = await this.service.sendAudio(
      instanceId,
      accountId,
      phone,
      audio
    )
    return result
  }

  async sendAudioFile(
    instanceId: string,
    accountId: string,
    userId: string,
    phone: string,
    audio: MinioFile
  ) {
    const now = new Date()
    const date = dateToString(now, 'YYYYMMDDTHHMMSS')

    const extension = getExtensionFromMimeType(audio.mimetype)
    audio.originalname = `${date}.${extension}`

    const midiaUrl = await this.minio.uploadFile(
      accountId,
      instanceId,
      phone,
      'audio',
      audio
    )

    const sendResult = await this.service.sendAudio(
      instanceId,
      accountId,
      phone,
      midiaUrl
    )

    if (sendResult.error) {
      console.log('FALHA AO ENVIAR O AUDIO', sendResult)
      return sendResult
    }

    await this.repository.saveMessageMedia(
      instanceId,
      accountId,
      userId,
      phone,
      sendResult.data,
      'audio',
      midiaUrl
    )
    return sendResult
  }

  async sendFile(
    instanceId: string,
    accountId: string,
    userId: string,
    phone: string,
    file: MinioFile,
    mediaType: string,
    caption?: string
  ) {
    const mediaUrl = await this.minio.uploadFile(
      accountId,
      instanceId,
      phone,
      mediaType,
      file
    )
    const sendResult = await this.service.sendFile(
      instanceId,
      accountId,
      phone,
      mediaUrl,
      mediaType,
      file.originalname,
      caption
    )

    if (sendResult.error) {
      console.log('FALHA AO ENVIAR ARQUIVO', sendResult)
      return sendResult
    }

    await this.repository.saveMessageMedia(
      instanceId,
      accountId,
      userId,
      phone,
      sendResult.data,
      mediaType,
      mediaUrl
    )

    return sendResult
  }

  async retrieverMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const result = await this.service.retrieverMediaMessage(
      instanceId,
      accountId,
      messageId
    )

    return result
  }

  async getBase64FromMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const result = await this.service.getBase64FromMediaMessage(
      instanceId,
      accountId,
      messageId
    )

    return result
  }

  async sniperConnect(
    instanceId: string,
    accountId: string,
    publicSniperId: string,
    enabled: boolean,
    jwt: string
  ) {
    const response = await this.service.findIntegration(
      instanceId,
      accountId,
      jwt
    )
    const integrationExists = !!response.data && response.data.length > 0

    if (integrationExists) {
      const integration = response.data[0]
      return await this.service.sniperUpdate(
        instanceId,
        accountId,
        publicSniperId,
        enabled,
        jwt,
        integration.id
      )
    } else {
      return await this.service.sniperCreate(
        instanceId,
        accountId,
        publicSniperId,
        enabled,
        jwt
      )
    }
  }

  async resendText(
    instanceId: string,
    phone: string,
    userId: string,
    accountId: string,
    messageId: string
  ) {
    // pegando a mensagem pelo ID
    const messageDocRef = this.repository.getMessageDocReference(
      accountId,
      instanceId,
      phone,
      messageId
    )

    // dados da mensagem
    const messageDoc = (await messageDocRef.get()).data()
    // mensagem de texto enviada anteriormente
    const message = messageDoc?.message

    // enviando novamente a mensagem
    const result = await this.sendText(
      instanceId,
      phone,
      message,
      userId,
      accountId,
      false
    )

    if (!result.error) {
      await messageDocRef.delete()
    }
    return result
  }

  async markMessageAsRead(
    instanceId: string,
    accountId: string,
    readMessages: [
      {
        remoteJid: string
        fromMe: boolean
        id: string
      },
    ]
  ) {
    const response: any = await this.service.markMessageAsRead(
      instanceId,
      accountId,
      readMessages
    )

    if (response.error === false) {
      readMessages.forEach(async (message) => {
        const options = { read: true }
        await this.repository.updateMessage(
          instanceId,
          accountId,
          message,
          options
        )
      })
    }

    const result = {
      error: response.error,
      data: response.data,
    }

    return result
  }

  async onConnectionUpdate(body: any) {
    const { state, statusReason, owner } = body.data

    const instanceId = body.instanceId
    const accountId = body.accountId

    const instanceStatus = {
      accountId,
      instanceId,
      status: {
        state,
        statusReason,
        owner: owner || null,
      },
    } as instanceStatusDto

    // Se já estiver conectado
    if (state == CodeChatInstanceStatusEnum.OPEN) {
      // Pega os dados da instância
      const fetchResult = await this.service.fetchInstances(
        instanceId,
        accountId,
        null
      )

      // Verifica se há dados
      if (fetchResult.error === false && fetchResult.data) {
        const results = fetchResult.data
        // Se houver dados
        if (results && results.length > 0) {
          // Pega os dados
          // TODO: Melhorar essa consulta pois a evolution tem um ID interno da instance, ao inves do name
          const result = results.find(
            (r: any) => r.instance?.instanceName === body.instanceName
          )

          // Verifica se houve dados
          if (result) {
            const { owner, profileName, profilePictureUrl } = result.instance

            // Atualiza os dados
            if (owner) {
              instanceStatus.status.owner = owner
            }

            if (profileName) {
              instanceStatus.status.profile = profileName
            }

            if (profilePictureUrl) {
              instanceStatus.status.profilePicture = profilePictureUrl
            }
          }
        }
      }
    }

    // Atualiza os dados no Firestore
    this.repository.updateInstanceStatus(instanceStatus)

    if (instanceStatus.status.state === CodeChatInstanceStatusEnum.OPEN) {
      const log = {
        data: {
          id: instanceStatus.instanceId,
          accountId: instanceStatus.accountId,
          status: instanceStatus.status.state,
        },
      }
      this.logs.createLogs(log, 'instance_connected', null, null)
    }

    const response = socketResponseModel
    response.data = instanceStatus
    response.state = state
    response.context = 'instance'

    ioSendData(instanceId, response)
  }

  async onQrCodeUpdate(body: any) {
    const { qrcode } = body.data
    const code = qrcode?.code
    if (!code) return
    ioSendData(body.instanceId, { state: 'connecting', code, data: body.data })
  }

  async sendPresence(
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    presence: string,
    jwt: string
  ) {
    const response: any = await this.service.sendPresence(
      instanceId,
      accountId,
      phoneNumber,
      presence,
      jwt
    )

    const result = {
      error: response.error,
      data: response.data,
    }

    return result
  }

  async onMessageUpsert(
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    body: any
  ) {
    const { data } = body
    let lead = body.lead

    // Fluxo de Mensagens Recebidas
    if (body.isEdited) {
      // Fluxo de Mensagens Editadas
      await this.repository.saveEditedMessage(
        instanceId,
        accountId,
        phoneNumber,
        data
      )
    } else {
      // Fluxo de Novas Mensagens Recebidas
      await this.repository.saveNewMessage(
        instanceId,
        accountId,
        phoneNumber,
        data
      )

      // Se for uma mensagem recebida, cria o lead se não houver e integra ao sniper
      if (!body.fromMe) {
        if (!lead) {
          lead = await this.createLead(body)
          body.lead = lead
        }

        const lastFromMe = await this.repository.getLastFromMe(
          accountId,
          instanceId,
          phoneNumber
        )
        if (
          lastFromMe &&
          lastFromMe.shotxCron &&
          lastFromMe.shotxCron.replied == false
        ) {
          this.repository.saveMessage(
            accountId,
            instanceId,
            phoneNumber,
            lastFromMe.id,
            {
              shotxCron: {
                replied: true,
              },
            },
            true
          )
          this.repository.addCronLogs(lastFromMe, REPLIED_TRIGGER)
        }

        if (!data.message.conversation) {
          return
        }

        const instanceResponse = await this.instances.get(accountId, instanceId)
        const instance = instanceResponse.data

        if (instanceResponse.error || !instance) {
          console.error('Error saving message, instance not found')
          return
        }

        // Responder apenas mensagem de text por enquanto
        const response = await this.sniper.messageReceived(
          instance,
          phoneNumber,
          phoneNumber,
          data.pushName,
          data.message.conversation,
          'text',
          lead
        )

        this.sendReply(instance, phoneNumber, response.messages)
      }
    }
  }

  async sendReply(
    instance: InstanceType,
    phone: string,
    messages: SniperResponse[],
    shotxCron?: string
  ) {
    // console.log('SEND REPLY', { instance, phone, messages, shotxCron })
    const { id, accountId, userId } = instance
    const results: any = []
    for (const { type, message } of messages) {
      console.log('SEND REPLY INSIDE', { type, message })
      switch (type) {
        case 'text':
          await this.sendText(
            id,
            phone,
            message,
            userId,
            accountId,
            true,
            shotxCron
          ).then((result) => {
            results.push(result)
          })
          break
        case 'image':
          // case 'video':
          await this.service
            .sendFile(id, accountId, phone, message, type, '')
            .then((result) => {
              results.push(result)
            })
          break
        // case 'audio':
        //   this.service.sendAudio(
        //     instanceId,
        //     accountId,
        //     phone,
        //     message
        //   )
        // break
        // case 'button':
        //   this.service.sendButton(senderId, message, accessToken)
        //   break
        default:
          break
      }
    }
    return results
  }

  async onMessageEdited(
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    data: any
  ) {
    await this.repository.saveEditedMessage(
      instanceId,
      accountId,
      phoneNumber,
      data
    )
  }

  async onMessageUpdate(accountId: string, instanceId: string, data: any) {
    console.log('UPDATE MESSAGE DOIDO')
    const deleted = this.isMessageDeleted(data)
    const options = { deleted }
    await this.repository.updateMessage(instanceId, accountId, data, options)
  }

  async onMessageDelete(accountId: string, instanceId: string, body: any) {
    console.log('Delete message', body)

    const options = { deleted: true }

    await this.repository.updateMessage(instanceId, accountId, body, options)
  }

  async onProfileUpdate(accountId: string, instanceId: string, data: any) {
    const instanceDB = await this.repository.getInstance(accountId, instanceId)

    if (!instanceDB || !instanceDB.status) return

    const current = {
      profile: instanceDB.status?.profile || '',
      profilePicture: instanceDB.status?.profilePicture || '',
      owner: instanceDB.status?.owner || '',
    }

    const newStatus = {
      profile: data.profileName || '',
      profilePicture: data.profilePicture || '',
      owner: data.owner || '',
    }

    if (current === newStatus) return

    const instanceStatus = {
      accountId,
      instanceId,
      status: {
        ...current,
        ...newStatus,
      },
    } as instanceStatusDto

    await this.repository.updateInstanceStatus(instanceStatus)
  }

  async webhookReceived(body: MessageUpSert) {
    const instanceId = body.instanceId
    const accountId = body.accountId
    const phoneNumber = body.phoneNumber
    console.log('Event', body?.event)
    switch (body?.event) {
      case EvolutionEvent.CONNECTION_UPDATE:
        this.onConnectionUpdate(body)
        break
      case EvolutionEvent.QRCODE_UPDATED:
        this.onQrCodeUpdate(body)
        break
      case EvolutionEvent.MESSAGES_UPSERT: // Mensagens Recebidas
      case EvolutionEvent.SEND_MESSAGE: // Mensagens Enviadas
        this.onMessageUpsert(accountId, instanceId, phoneNumber, body)
        break
      case EvolutionEvent.MESSAGES_UPDATE:
        this.onMessageUpdate(accountId, instanceId, body.data)
        break
      case EvolutionEvent.PROFILE_UPDATE:
        this.onProfileUpdate(accountId, instanceId, body.data)
        break
      case EvolutionEvent.MESSAGE_DELETE:
        this.onMessageDelete(accountId, instanceId, body)
        break
      case EvolutionEvent.MESSAGE_EDITED:
        this.onMessageEdited(accountId, instanceId, phoneNumber, body.data)
        break
      default:
        console.log('Unknown event received', body?.event)
        break
    }
  }

  private isMessageEdited(data: any): boolean {
    return data?.messageType == 'editedMessage'
  }

  private isMessageDeleted(data: any): boolean {
    const { message, status } = data
    return !message && !status
  }

  private messageId(data: any): string {
    return (
      data?.key?.id ||
      data?.message?.editedMessage?.message?.protocolMessage?.key?.id
    )
  }

  private contactId = (data: any): string => {
    return data?.key.remoteJid
  }

  public async onSniperSessionClose(
    session: SessionRef,
    matchedVars: any,
    lead?: LeadType
  ) {
    const { accountId, contactId } = session
    const { phoneOnly, countryCode } = getPhoneFromContactId(contactId)
    const { mobile, mobileCC } = matchedVars

    // Se foi informado um número no fluxo
    if (!!mobile && !!mobileCC) {
      // Se o telefone informado no fluxo for diferente do lead encontrado
      if (lead?.mobile != mobile || lead?.mobileCC != mobileCC) {
        // Busca o lead pelo (mobile, email, telefone) *EXATAMENTE NESSA ORDEM
        lead = await this.findLeadByResults(accountId, matchedVars)
      }
    } else {
      // Se não foi informado, segue o fluxo normal (buscar pelo contact)
      // Se não tiver um lead, então busca o lead pelo contactId
      lead ??= await LeadController.findByMobile(
        accountId,
        phoneOnly,
        countryCode
      )
    }

    // Se encontrou um lead
    if (lead) {
      // Atualizar o lead
      LeadController.updateLeadWithSniperResults(lead, matchedVars)
    } else {
      // Criar o lead
      this.createLeadWithResults(session, matchedVars)
    }
  }

  public async createLead(body: MessageUpSert) {
    if (body.lead) {
      console.log('Lead already exists: ', {
        currentId: body.lead.id || body.lead,
      })
      return body.lead
    }

    const eventData = sanitizeUpSertEventData(body)

    // TODO: verificar se é somente comparar o remoteid com o owner
    if (eventData.phone === 'status') {
      return
    }

    const mobile = eventData.phone
    const mobileCC = eventData.countryCode

    if (!mobile || !mobileCC) {
      return
    }

    const lead = {
      ...LeadModel,
      firstName: eventData.firstName,
      lastName: eventData.lastName,
      displayName: eventData.pushName,
      mobile: mobile,
      mobileCC: mobileCC,
      accountId: eventData.accountId,
      origin: DATA_ORIGINS.SHOTX.WHATSAPP,
      instanceId: eventData.instanceId,
      // locale: eventData.locale,
      context: {
        ...ContextModel,
        id: eventData.accountId,
        collection: SHOTX_COLLECTION_NAME,
        user_agent: eventData.source,
        origin: DATA_ORIGINS.SHOTX.WHATSAPP,
        instanceId: eventData.instanceId,
      },
    }

    await LeadController.createLead(lead)
    return await LeadController.findByMobile(lead.accountId, mobile, mobileCC)
  }

  public async createLeadWithResults(session: SessionRef, matchedVars: any) {
    const { accountId, instanceId } = session
    const { mobile, mobileCC } = matchedVars

    let lead = {
      ...LeadModel,
      accountId: accountId,
      origin: DATA_ORIGINS.SHOTX.WHATSAPP,
      instanceId: instanceId,
      context: {
        ...ContextModel,
        id: accountId,
        collection: SHOTX_COLLECTION_NAME,
        user_agent: 'sniper',
        origin: DATA_ORIGINS.SHOTX.WHATSAPP,
        instanceId: instanceId,
      },
    }
    lead = mergeObject(lead, matchedVars)

    await LeadController.createLead(lead)
    return await LeadController.findByMobile(lead.accountId, mobile, mobileCC)
  }

  public async findLeadByResults(accountId: string, matchedVars: any) {
    const { email, mobile, mobileCC, phone, phoneCC } = matchedVars as any

    // Busca pelo mobile
    if (!!mobile && !!mobileCC) {
      const lead = await LeadController.findByMobile(
        accountId,
        mobile,
        mobileCC
      )
      console.log('LEAD COM MOBILE?', !!lead)
      if (lead) return lead
    }

    // Se não encontrou lead, busca pelo email
    if (email) {
      const lead = await LeadController.findByEmail(accountId, email)
      console.log('LEAD COM EMAIL?', !!lead)
      if (lead) return lead
    }

    // Se não encontrou lead, busca pelo phone
    if (!!phone && !!phoneCC) {
      const lead = await LeadController.findByPhone(accountId, phone, phoneCC)
      console.log('LEAD COM PHONE?', !!lead)
      if (lead) return lead
    }

    return
  }
}

export default new WhatsappController(
  new EvolutionService(),
  new WhatsappRepository(),
  new MinioRepository(),
  new SniperController(),
  new InstancesRepository()
)
