import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { deleteKey } from '../../../libs/redis/redisClient'

export async function removeMessage(message: any, messageKey: string) {
  try {
    // Extrair o ID do agendamento (shotxCronId)
    let appointmentId = message.appointment_id || message.shotxCronId

    if (!appointmentId && message.id) {
      appointmentId = message.id.split('_')[0]
    }

    const sanitizedAppointmentId = appointmentId?.toString().trim()

    // Validar todos os dados obrigatórios
    if (!sanitizedAppointmentId || sanitizedAppointmentId.length === 0) {
      throw new Error('appointmentId is missing or invalid.')
    }

    if (
      !FIRESTORE_COLLECTIONS.SHOTXCRON ||
      typeof FIRESTORE_COLLECTIONS.SHOTXCRON !== 'string'
    ) {
      throw new Error(
        'FIRESTORE_COLLECTIONS.SHOTXCRON is not properly defined.'
      )
    }

    if (
      !FIRESTORE_COLLECTIONS.FAILS ||
      typeof FIRESTORE_COLLECTIONS.FAILS !== 'string'
    ) {
      throw new Error('FIRESTORE_COLLECTIONS.FAILS is not properly defined.')
    }

    // Montar o documento a ser salvo
    const docToSave = {
      id: message.id,
      failedAt: new Date().toISOString(),
      originalMessageKey: messageKey,
      ...message,
    }

    // Criar referência ao documento pai e subcoleção
    const parentRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTXCRON)
      .doc(sanitizedAppointmentId)

    const failsRef = parentRef.collection(FIRESTORE_COLLECTIONS.FAILS)

    // Adicionar o documento
    const docRef = await failsRef.add(docToSave)

    console.log(`Mensagem com falha salva com ID: ${docRef.id}`)

    await deleteKey(message.redis_key)
    return
  } catch (error) {
    console.error('Erro ao salvar mensagem com falha:', error)
    throw error
  }
}
